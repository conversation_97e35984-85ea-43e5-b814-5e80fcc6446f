# Testing Guide for Inventory Tracker App

## Overview
This guide outlines the comprehensive testing strategy for each task in the Inventory Tracker App development. Testing is mandatory after each task completion to ensure quality and prevent regressions.

## Testing Principles

### 1. Test After Every Task Completion
- **Never skip testing** - Each task must be tested before marking as complete
- **Document test results** - Keep a record of what was tested and results
- **Fix issues immediately** - Don't move to the next task with known bugs

### 2. Testing Types Required

#### Unit Tests
- Test individual functions and components in isolation
- Use Jest for JavaScript/React components
- Use pytest for Python backend functions
- Aim for >80% code coverage on new code

#### Integration Tests
- Test how components work together
- Test API endpoints with real database
- Test OCR service integration
- Test Docker container interactions

#### End-to-End Tests
- Test complete user workflows
- Use real data when possible
- Test in Docker environment
- Include mobile device testing

#### Performance Tests
- Test with realistic data volumes
- Monitor memory and CPU usage
- Test response times
- Verify Docker resource consumption

## Task-Specific Testing Requirements

### Task 1: Multi-Page PDF Upload System

#### 1.1 Frontend Upload Component
**Required Tests:**
- Upload single PDF (backward compatibility)
- Upload multi-page PDF (2-10 pages)
- Upload large PDF files (>10MB)
- Drag and drop functionality
- File validation (PDF only, size limits)
- Preview functionality
- Error handling for invalid files

**Test Files Needed:**
- Single page invoice PDF
- Multi-page invoice PDF (3-5 pages)
- Large PDF file (>10MB)
- Corrupted PDF file
- Non-PDF file (for validation testing)

**Success Criteria:**
- All file types handled correctly
- Preview shows all pages
- Upload progress works
- Error messages are clear
- Mobile responsive

#### 1.2 PDF Splitting Backend Logic
**Required Tests:**
- Split 2-page PDF correctly
- Split 5+ page PDF correctly
- Handle encrypted PDFs
- Handle corrupted PDFs
- Preserve page order
- Memory usage with large files

**Test Commands:**
```bash
# Run unit tests
cd backend
python -m pytest tests/test_pdf_splitting.py -v

# Test with sample files
python -m pytest tests/test_pdf_integration.py -v
```

**Success Criteria:**
- All pages extracted correctly
- Page order maintained
- Error handling works
- Memory usage acceptable
- No data corruption

#### 1.3 Database Schema Updates
**Required Tests:**
- Migration runs successfully
- Foreign key constraints work
- Data integrity maintained
- Performance with large datasets
- Rollback migration works

**Test Commands:**
```bash
# Test migrations
cd backend
python manage.py migrate --check
python manage.py test tests.test_models

# Test in Docker
docker-compose exec backend python manage.py test
```

**Success Criteria:**
- Migration completes without errors
- All constraints enforced
- Query performance acceptable
- Data relationships correct

#### 1.4 OCR Processing Enhancement
**Required Tests:**
- Process single page (existing functionality)
- Process multi-page invoice
- Test with Bova vendor invoices
- Test with Kast vendor invoices
- Verify result consolidation
- Test error handling

**Test Files:**
- Use real Bova multi-page invoice
- Use real Kast multi-page invoice
- Test with generic vendor invoice

**Success Criteria:**
- OCR accuracy maintained (>95%)
- All pages processed
- Results properly consolidated
- Existing functionality unaffected

### Task 2: Price Tracking and Alert System

#### 2.1 Price History Database Schema
**Required Tests:**
- Schema creation successful
- Index performance testing
- Data insertion/retrieval
- Foreign key relationships
- Large dataset performance

#### 2.2 Price Change Detection
**Required Tests:**
- Detect price increases correctly
- Detect price decreases
- Handle first-time items
- Calculate percentage changes
- Test with edge cases (zero prices, etc.)

#### 2.3 Alert Engine
**Required Tests:**
- Alert triggering logic
- Email notification delivery
- Alert frequency controls
- Threshold configurations
- Alert deduplication

#### 2.4 Price Analytics Dashboard
**Required Tests:**
- Dashboard loads with real data
- Charts render correctly
- Mobile responsiveness
- Data filtering works
- Export functionality

## Testing Environment Setup

### Local Testing
```bash
# Start test environment
docker-compose -f docker-compose.test.yml up -d

# Run backend tests
docker-compose exec backend python -m pytest

# Run frontend tests
docker-compose exec frontend npm test

# Run integration tests
docker-compose exec backend python manage.py test tests.integration
```

### Test Data Requirements
- Sample multi-page PDFs (Bova, Kast, Generic)
- Historical invoice data for price testing
- Test user accounts
- Sample vendor data

### Mobile Testing
- Test on actual mobile devices
- Use Chrome DevTools mobile simulation
- Test touch interactions
- Verify responsive design

## Test Documentation

### For Each Task Completion:
1. **Create test report** in `docs/test-reports/task-X.X-test-report.md`
2. **Include screenshots** of key functionality
3. **Document any issues found** and how they were resolved
4. **Record performance metrics** (response times, memory usage)
5. **Verify Docker compatibility**

### Test Report Template:
```markdown
# Test Report: Task X.X - [Task Name]

## Date: [Date]
## Tester: [Name]

## Tests Performed:
- [ ] Unit tests passed
- [ ] Integration tests passed
- [ ] End-to-end tests passed
- [ ] Performance tests passed
- [ ] Mobile tests passed

## Issues Found:
1. [Issue description] - [Resolution]

## Performance Metrics:
- Response time: [X]ms
- Memory usage: [X]MB
- CPU usage: [X]%

## Screenshots:
[Include relevant screenshots]

## Conclusion:
[Pass/Fail with reasoning]
```

## Continuous Testing

### Automated Testing
- Set up GitHub Actions for automated testing
- Run tests on every commit
- Include Docker build testing
- Test against multiple environments

### Regression Testing
- Re-run critical tests after each task
- Verify existing OCR functionality
- Test user authentication
- Verify Docker deployment

## Quality Gates

### Before Marking Task Complete:
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] End-to-end tests pass
- [ ] Performance requirements met
- [ ] Mobile compatibility verified
- [ ] Docker deployment tested
- [ ] Test report completed
- [ ] Code reviewed
- [ ] Documentation updated

### Before Moving to Next Task:
- [ ] Previous task fully tested
- [ ] No critical bugs remaining
- [ ] Performance acceptable
- [ ] User acceptance criteria met

## Emergency Procedures

### If Tests Fail:
1. **Stop development** on new features
2. **Document the failure** in detail
3. **Fix the issue** before proceeding
4. **Re-run all tests** to verify fix
5. **Update test cases** if needed

### If Performance Issues Found:
1. **Profile the application** to identify bottlenecks
2. **Optimize code** or queries
3. **Re-test performance**
4. **Document optimizations made**

Remember: **Quality is more important than speed. Never skip testing to save time.**
