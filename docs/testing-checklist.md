# Testing Checklist - Quick Reference

## Before Starting Any Task
- [ ] Ensure current Docker environment is working
- [ ] Verify existing OCR functionality is intact
- [ ] Create test branch: `git checkout -b task-X.X-[task-name]`
- [ ] Document current system state

## After Completing Each Subtask

### 1.1 Frontend Upload Component
- [ ] Test single PDF upload (backward compatibility)
- [ ] Test multi-page PDF upload (2-5 pages)
- [ ] Test large PDF file (>10MB)
- [ ] Test drag and drop functionality
- [ ] Test file validation (PDF only)
- [ ] Test preview functionality
- [ ] Test error handling for invalid files
- [ ] Test on mobile device
- [ ] Verify responsive design
- [ ] **Create test report**: `docs/test-reports/task-1.1-test-report.md`

### 1.2 PDF Splitting Backend Logic
- [ ] Run unit tests: `python -m pytest tests/test_pdf_splitting.py -v`
- [ ] Test with 2-page PDF
- [ ] Test with 5+ page PDF
- [ ] Test with encrypted PDF
- [ ] Test with corrupted PDF
- [ ] Verify page order preservation
- [ ] Test memory usage with large files
- [ ] Test in Docker environment
- [ ] **Create test report**: `docs/test-reports/task-1.2-test-report.md`

### 1.3 Database Schema Updates
- [ ] Test migration: `python manage.py migrate --check`
- [ ] Run model tests: `python manage.py test tests.test_models`
- [ ] Test foreign key constraints
- [ ] Test data integrity
- [ ] Test performance with large datasets
- [ ] Test rollback migration
- [ ] Test in Docker: `docker-compose exec backend python manage.py test`
- [ ] **Create test report**: `docs/test-reports/task-1.3-test-report.md`

### 1.4 OCR Processing Enhancement
- [ ] Test single page processing (existing functionality)
- [ ] Test multi-page invoice processing
- [ ] Test with real Bova vendor invoice
- [ ] Test with real Kast vendor invoice
- [ ] Test with generic vendor invoice
- [ ] Verify result consolidation
- [ ] Test error handling
- [ ] Verify OCR accuracy >95%
- [ ] Test in Docker environment
- [ ] **Create test report**: `docs/test-reports/task-1.4-test-report.md`

## After Completing Task 1 (Multi-Page PDF Upload)
- [ ] End-to-end test: Upload multi-page PDF → Process → Review results
- [ ] Performance test with large PDFs
- [ ] Test backward compatibility with existing single-page workflow
- [ ] Test on mobile device
- [ ] Verify Docker deployment works
- [ ] **Create comprehensive test report**: `docs/test-reports/task-1-complete-test-report.md`

## Price Tracking Tasks (Task 2)

### 2.1 Price History Database Schema
- [ ] Test schema creation
- [ ] Test index performance
- [ ] Test data insertion/retrieval
- [ ] Test foreign key relationships
- [ ] Test with large datasets
- [ ] **Create test report**: `docs/test-reports/task-2.1-test-report.md`

### 2.2 Price Change Detection Logic
- [ ] Test price increase detection
- [ ] Test price decrease detection
- [ ] Test first-time item handling
- [ ] Test percentage calculations
- [ ] Test edge cases (zero prices, negative prices)
- [ ] Test with real invoice data
- [ ] **Create test report**: `docs/test-reports/task-2.2-test-report.md`

### 2.3 Alert Engine
- [ ] Test alert triggering logic
- [ ] Test email notification delivery
- [ ] Test alert frequency controls
- [ ] Test threshold configurations
- [ ] Test alert deduplication
- [ ] Test with different user preferences
- [ ] **Create test report**: `docs/test-reports/task-2.3-test-report.md`

### 2.4 Price Analytics Dashboard
- [ ] Test dashboard loading with real data
- [ ] Test chart rendering
- [ ] Test mobile responsiveness
- [ ] Test data filtering
- [ ] Test search functionality
- [ ] Test export functionality
- [ ] **Create test report**: `docs/test-reports/task-2.4-test-report.md`

## Critical Regression Tests (Run After Every Task)
- [ ] User login/authentication works
- [ ] Single-page invoice upload works
- [ ] OCR processing for Bova invoices works
- [ ] OCR processing for Kast invoices works
- [ ] Docker containers start properly
- [ ] Database connections work
- [ ] Frontend loads without errors

## Performance Benchmarks
- [ ] Invoice upload time: < 5 seconds
- [ ] OCR processing time: < 30 seconds per page
- [ ] Dashboard load time: < 3 seconds
- [ ] Database query time: < 1 second
- [ ] Memory usage: < 2GB total
- [ ] CPU usage: < 80% during processing

## Mobile Testing Requirements
- [ ] Test on actual mobile device (iOS/Android)
- [ ] Test touch interactions
- [ ] Test responsive design
- [ ] Test file upload on mobile
- [ ] Test navigation on small screens

## Docker Testing Requirements
- [ ] `docker-compose up` works without errors
- [ ] All services start properly
- [ ] Database migrations run automatically
- [ ] Frontend builds successfully
- [ ] Backend API responds correctly
- [ ] OCR service integrations work

## Quality Gates - Never Skip These!

### Before Marking Subtask Complete:
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Test report created
- [ ] No critical bugs found

### Before Marking Task Complete:
- [ ] All subtasks tested and complete
- [ ] End-to-end testing passed
- [ ] Performance requirements met
- [ ] Mobile compatibility verified
- [ ] Docker deployment tested
- [ ] Regression tests passed
- [ ] Comprehensive test report created

### Before Moving to Next Task:
- [ ] Previous task fully tested and documented
- [ ] No known critical issues
- [ ] Code reviewed and approved
- [ ] Documentation updated

## Emergency Procedures

### If Any Test Fails:
1. **STOP** - Do not continue to next task
2. **Document** the failure in detail
3. **Fix** the issue immediately
4. **Re-run** all related tests
5. **Update** test cases if needed
6. **Only then** mark task as complete

### If Performance Issues Found:
1. **Profile** the application
2. **Identify** bottlenecks
3. **Optimize** code/queries
4. **Re-test** performance
5. **Document** optimizations

## Test Data Files Needed
- `test-data/single-page-invoice.pdf`
- `test-data/multi-page-bova-invoice.pdf`
- `test-data/multi-page-kast-invoice.pdf`
- `test-data/large-invoice.pdf` (>10MB)
- `test-data/corrupted-file.pdf`
- `test-data/non-pdf-file.txt`

## Quick Test Commands
```bash
# Start test environment
docker-compose -f docker-compose.test.yml up -d

# Run all backend tests
docker-compose exec backend python -m pytest -v

# Run frontend tests
docker-compose exec frontend npm test

# Run specific test file
docker-compose exec backend python -m pytest tests/test_pdf_splitting.py -v

# Check test coverage
docker-compose exec backend python -m pytest --cov=. --cov-report=html
```

**Remember: Testing is not optional. Quality over speed always!**
